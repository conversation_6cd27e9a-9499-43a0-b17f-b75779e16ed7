<template>
  <Teleport to="body">
    <div v-if="visible" class="community-book-detail-modal" @click="handleMaskClick">
      <el-config-provider :locale="zhCn">
        <div class="community-book-detail-dialog" @click.stop>
          <!-- 弹窗头部 -->
          <div class="dialog-header">
            <div class="dialog-title">
              社区图书数据详情
              <!-- <span class="time-range-info">（{{ getTimeRangeText() }}）</span> -->
            </div>
            <div class="close-btn" @click="handleClose">×</div>
          </div>

          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form
              ref="ruleFormRef"
              :model="searchForm"
              :inline="true"
              label-width="80px"
              class="search-form-content"
            >
              <el-form-item label="关键字" prop="key">
                <el-input
                  v-model="searchForm.key"
                  placeholder="请输入名称或区域名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
                <el-button :icon="Refresh" @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据列表表格 -->
          <div class="table-box">
            <el-table
              :max-height="'calc(100vh - 280px)'"
              :data="dataList"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-background="rgba(0, 0, 0, 0.6)"
              class="community-book-table"
            >
              <el-table-column label="名称" prop="name" align="center" min-width="120">
                <template #default="{ row }">
                  <span class="book-name">{{ row.name || '未知' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="类型" prop="type" align="center" width="150">
                <template #default="{ row }">
                  <el-tag :type="getBookTypeTag(row.type)" size="small">
                    {{ row.type || '未分类' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="所属区域" prop="districtName" align="center" width="120">
                <template #default="{ row }">
                  <span>{{ row.districtName || '未知区域' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="经度" prop="longitude" align="center" width="140">
                <template #default="{ row }">
                  <span class="coordinate-text" :title="row.longitude || '暂无数据'">
                    {{ formatCoordinate(row.longitude) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="纬度" prop="latitude" align="center" width="140">
                <template #default="{ row }">
                  <span class="coordinate-text" :title="row.latitude || '暂无数据'">
                    {{ formatCoordinate(row.latitude) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="创建时间" prop="createTime" align="center" width="180">
                <template #default="{ row }">
                  <span>{{ formatTime(row.createTime) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-box">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :small="false"
              :disabled="false"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="pagination-component"
            />
          </div>
        </div>
      </el-config-provider>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getCommunityBookDataDetailApi } from '@/api/community'
import type { CommunityBookDataDetailParams, CommunityBookDataDetailItem } from '@/api/community'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 组件Props
interface Props {
  visible: boolean
  areaCode?: string
  timeRange?: string  // 时间范围参数
}

// 组件Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  areaCode: '510183',
  timeRange: '2'
})

const emit = defineEmits<Emits>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 搜索表单数据
const searchForm = reactive({
  key: ''
})

// 列表数据
const dataList = ref<CommunityBookDataDetailItem[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时重置数据并加载
    resetSearch()
    loadData()
  }
})

// 监听时间范围变化
watch(() => props.timeRange, () => {
  if (props.visible) {
    loadData()
  }
})

// 监听区域代码变化
watch(() => props.areaCode, () => {
  if (props.visible) {
    loadData()
  }
})

// 加载数据
const loadData = async () => {
  if (loading.value) return

  loading.value = true

  try {
    const params: CommunityBookDataDetailParams = {
      timeType: props.timeRange || '2',
      areaCode: props.areaCode || '510183',
      key: searchForm.key || undefined,
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      orderByColumn: '',
      isAsc: 'asc'
    }

    console.log('📚 社区图书数据详情查询参数:', params)

    const response = await getCommunityBookDataDetailApi(params)

    console.log('📚 社区图书数据详情API响应:', response)

    // 根据实际API响应结构处理数据
    if (response && response.code === 200) {
      console.log('📚 API响应结构:', response)

      // 处理列表数据 - 接口返回的是 { total, rows, code, msg } 格式
      if (response.rows && Array.isArray(response.rows)) {
        dataList.value = response.rows.map((item: any) => {
          // 调试日志：检查原始数据中的经纬度字段
          console.log('📍 原始数据项:', {
            name: item.name,
            longitude: item.longitude,
            latitude: item.latitude,
            lng: item.lng,
            lat: item.lat,
            lon: item.lon,
            allKeys: Object.keys(item)
          })

          return {
            id: item.id || Math.random().toString(),
            name: item.name || '未知图书',
            type: item.typeName || item.type || '未分类',
            status: item.status || 'available',
            districtName: item.districtName || '未知区域',
            longitude: item.longitude || item.lng || item.lon || '',
            latitude: item.latitude || item.lat || '',
            browseNumber: item.total || item.browseNumber || item.borrowCount || 0,
            createTime: item.createTime || '',
            updateTime: item.updateTime || item.modifyTime || ''
          }
        })

        // 处理分页信息
        total.value = response.total || 0
        // currentPage 和 pageSize 保持当前值，因为接口没有返回这些字段

        console.log('📚 处理后的图书列表数据:', dataList.value)
        console.log('📚 分页信息 - 总数:', total.value, '当前页:', currentPage.value, '页大小:', pageSize.value)
      } else {
        console.warn('⚠️ 社区图书数据详情API返回的rows字段不是数组:', response.rows)
        dataList.value = []
        total.value = 0
      }
    } else {
      console.warn('⚠️ 社区图书数据详情API返回异常:', response)
      dataList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('❌ 获取社区图书数据详情失败:', error)
    ElMessage.error('获取社区图书数据详情失败')
    dataList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  console.log('🔍 搜索社区图书数据，关键字:', searchForm.key)
  currentPage.value = 1 // 搜索时重置到第一页
  loadData()
}

// 处理重置
const handleReset = () => {
  console.log('🔄 重置社区图书数据搜索条件')
  searchForm.key = ''
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.key = ''
  currentPage.value = 1
  pageSize.value = 10
  total.value = 0
  dataList.value = []
}

// 处理页大小变化
const handleSizeChange = (newSize: number) => {
  console.log('📄 社区图书数据详情页大小变化:', newSize)
  pageSize.value = newSize
  currentPage.value = 1 // 改变页大小时重置到第一页
  loadData()
}

// 处理当前页变化
const handleCurrentChange = (newPage: number) => {
  console.log('📄 社区图书数据详情当前页变化:', newPage)
  currentPage.value = newPage
  loadData()
}

// 处理关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 处理遮罩点击
const handleMaskClick = () => {
  handleClose()
}

// 获取类型标签类型
const getBookTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    '武术培训班': 'primary',
    '摄影技巧': 'success',
    '25届中国舞蹈大赛': 'info',
    '亲子活动': 'warning',
    '美食探店': 'danger',
    '生活妙招': 'primary',
    '书法研习社': 'success',
    '其他': ''
  }
  return typeMap[type] || 'info'
}

// 获取状态标签类型
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    'available': 'success',
    'borrowed': 'warning',
    'maintenance': 'danger',
    'lost': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'available': '可借阅',
    'borrowed': '已借出',
    'maintenance': '维护中',
    'lost': '丢失',
    '1': '可借阅',
    '0': '不可借阅'
  }
  return statusMap[status] || '未知'
}

// 格式化坐标
const formatCoordinate = (coordinate: string | number) => {
  if (!coordinate || coordinate === '' || coordinate === null || coordinate === undefined) {
    return '-'
  }

  const num = typeof coordinate === 'string' ? parseFloat(coordinate) : coordinate

  if (isNaN(num)) {
    return '-'
  }

  // 保留6位小数
  return num.toFixed(6)
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '未知时间'

  try {
    const date = new Date(timeStr)
    if (isNaN(date.getTime())) {
      return timeStr // 如果无法解析，返回原字符串
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('📚 社区图书数据详情弹窗组件已挂载')
})
</script>

<style lang="scss" scoped>
.community-book-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.community-book-detail-dialog {
  background: #060E1F;
  border-radius: 8px;
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(74, 144, 226, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: #060E1F;
  border-radius: 8px 8px 0 0;

  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #00d4ff;

    .time-range-info {
      font-size: 14px;
      color: #9d9da6;
      font-weight: normal;
    }
  }

  .close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    cursor: pointer;
    font-size: 20px;
    color: #9d9da6;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: #fff;
    }
  }
}

.search-form {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: #060E1F;

  .search-form-content {
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 16px;

      .el-form-item__label {
        color: #9d9da6;
      }

      .el-input__wrapper {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #333;
        border-radius: 4px;

        .el-input__inner {
          color: #fff;
          background: transparent;

          &::placeholder {
            color: #666;
          }
        }
      }

      .el-button {
        &.el-button--primary {
          background: #1990ff;
          border-color: #1990ff;
        }
      }
    }
  }
}

.table-box {
  flex: 1;
  padding: 0 20px;
  overflow: hidden;
  background: #060E1F;

  .community-book-table {
    height: 100%;

    :deep(.el-table) {
      --el-table-bg-color: #060E1F !important;
      --el-table-tr-bg-color: #060E1F !important;
      --el-table-header-bg-color: #060E1F !important;
      --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
      --el-table-text-color: #ffffff !important;
      --el-table-header-text-color: #ffffff !important;
      --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
      background: #060E1F !important;
      color: #fff;

      .el-table__header {
        background: #060E1F !important;

        th {
          background: #060E1F !important;
          color: #00d4ff !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          font-weight: bold;
        }
      }

      .el-table__body {
        background: #060E1F !important;

        tr {
          background: #060E1F !important;

          &:hover {
            background: rgba(74, 144, 226, 0.15) !important;
          }

          td {
            background: #060E1F !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #fff !important;
          }
        }
      }

      .el-table__empty-block {
        background: #060E1F !important;
        color: #ffffff !important;
      }

      .el-table__empty-text {
        color: #ffffff !important;
      }
    }

    .book-name {
      font-weight: 500;
      color: #00d4ff;
    }

    .coordinate-text {
      color: #9d9da6;

      font-size: 14px;

      &:hover {
        color: #00d4ff;
      }
    }

    .borrow-count {
      font-weight: bold;
      color: #fba602;
    }

    :deep(.el-tag) {
      border: none;

      &.el-tag--primary {
        background: rgba(25, 144, 255, 0.2) !important;
        color: #1990ff !important;
      }

      &.el-tag--success {
        background: rgba(103, 194, 58, 0.2) !important;
        color: #67c23a !important;
      }

      &.el-tag--info {
        background: rgba(144, 147, 153, 0.2) !important;
        color: #909399 !important;
      }

      &.el-tag--warning {
        background: rgba(251, 166, 2, 0.2) !important;
        color: #fba602 !important;
      }

      &.el-tag--danger {
        background: rgba(245, 108, 108, 0.2) !important;
        color: #f56c6c !important;
      }
    }
  }
}

.pagination-box {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: #060E1F;
  border-radius: 0 0 8px 8px;

  .pagination-component {
    :deep(.el-pagination) {
      --el-pagination-bg-color: #060E1F !important;
      --el-pagination-text-color: #ffffff !important;
      --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
      --el-pagination-hover-color: #4a90e2 !important;
      background: #060E1F !important;

      .el-pagination__total,
      .el-pagination__jump {
        color: #ffffff !important;
        background: #060E1F !important;
      }

      .el-pager li {
        background: #060E1F !important;
        color: #ffffff !important;
        border: 1px solid rgba(74, 144, 226, 0.3) !important;

        &.is-active {
          background: #4A90E2 !important;
          color: #fff !important;
          border-color: #4A90E2 !important;
        }

        &:hover:not(.is-active) {
          background: rgba(74, 144, 226, 0.3) !important;
          color: #fff !important;
          border-color: #4A90E2 !important;
        }
      }

      .btn-prev,
      .btn-next {
        background: #060E1F !important;
        color: #ffffff !important;
        border: 1px solid rgba(74, 144, 226, 0.3) !important;

        &:hover:not(.is-disabled) {
          background: rgba(74, 144, 226, 0.3) !important;
          color: #fff !important;
          border-color: #4A90E2 !important;
        }

        &.is-disabled {
          background: rgba(255, 255, 255, 0.05) !important;
          color: rgba(255, 255, 255, 0.3) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
        }
      }

      .el-select .el-select__wrapper {
        background: #060E1F !important;
        border: 1px solid rgba(74, 144, 226, 0.3) !important;

        .el-select__inner {
          color: #fff !important;
          background: #060E1F !important;
        }
      }
    }
  }
}

// 深色模式全局覆盖
.community-book-detail-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
    background: #060E1F !important;

    .el-pagination__total,
    .el-pagination__jump,
    .el-pagination__sizes,
    .el-pagination__editor {
      background: #060E1F !important;
      color: #ffffff !important;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #060E1F !important;
      color: #ffffff !important;
      border: 1px solid rgba(74, 144, 226, 0.3) !important;
    }
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  .el-input__wrapper {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    .el-input__inner {
      color: #ffffff !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }

  .el-select .el-input .el-select__caret {
    color: #ffffff !important;
  }
}

// 全局强制深色样式覆盖
:deep(.el-pagination) {
  background: #060E1F !important;

  * {
    background: #060E1F !important;
    color: #ffffff !important;
  }

  .el-input__wrapper {
    background: #060E1F !important;
    border: 1px solid rgba(74, 144, 226, 0.3) !important;
  }

  .el-input__inner {
    background: #060E1F !important;
    color: #ffffff !important;
  }

  .btn-prev,
  .btn-next,
  .el-pager li {
    background: #060E1F !important;
    color: #ffffff !important;
    border: 1px solid rgba(74, 144, 226, 0.3) !important;
  }
}

// 强制覆盖所有可能的白色背景
:deep(.el-popper) {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;

    &:hover {
      background: rgba(74, 144, 226, 0.3) !important;
    }

    &.is-selected {
      background: #4A90E2 !important;
      color: #ffffff !important;
    }
  }
}
</style>
