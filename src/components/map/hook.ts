//边界
import * as mars3d from "mars3d";
import { simplify } from "@turf/turf";
import { data0 } from "./data/data0";
import { data1 } from "./data/data1";
import { data2 } from "./data/data2";
import { data3 } from "./data/data3";

let geoJsonLayer: mars3d.layer.GeoJsonLayer | null = null;
let thisMap: mars3d.Map;

export const eventTarget = new mars3d.BaseClass();

const dataArr = [data0, data1, data2, data3];
function esriToGeoJSON(esriJson: any) {
  const features = esriJson.features
    .filter((f: any) => f.geometry && f.geometry.rings) // 过滤掉无效 geometry
    .map((f: any) => {
      return {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: f.geometry.rings,
        },
        properties: f.attributes,
      };
    });

  return {
    type: "FeatureCollection",
    features,
  };
}
// 添加行政规划
export async function queryArcgisLayer(
  geoId: string,
  map: mars3d.Map,
  fun?: (pointData: any) => void
) {
  thisMap = map;
  const url = `https://api.cdmbc.cn:4432/gateway/gis/1/${geoId}`;
  const params: Record<string, string> = {
    AppKey: "700023938926772224",
    where: "1=1", // 查询所有
    outFields: "*", // 返回所有字段
    returnGeometry: "true", // 返回几何
    f: "json", // 请求GeoJSON格式
  };

  let simplifiedGeojson;

  if (geoId === "5e249977ce1b454eab17999b9122871e/0/query") {
    simplifiedGeojson = data0;
  } else if (geoId === "8780d3715f054a0b948bb3a744d8ba34/0/query") {
    simplifiedGeojson = data1;
  } else if (geoId === "5f651de6590d4583ad2943027c86cb9e/0/query") {
    simplifiedGeojson = data2;
  } else {
    const queryUrl = `${url}?${new URLSearchParams(params).toString()}`;
    const response = await fetch(queryUrl);
    const geojsonData = await response.json();

    const safeGeojson: any = esriToGeoJSON(geojsonData);
    // 这里优化性能
    simplifiedGeojson = simplify(safeGeojson, {
      tolerance: 0.0001, //越大加载越快
      highQuality: true, // 或 true，精度更高但慢
      mutate: true, //允许在原地修改 geojson
    });
  }
  // console.log("simplifiedGeojson----", simplifiedGeojson);

  infoArr.push({
    info: data0.features[0].properties,
  });
  // console.log("infoArr------", infoArr);

  //销毁旧图层
  if (geoJsonLayer) {
    map.removeLayer(geoJsonLayer, true); // true 表示彻底销毁
    geoJsonLayer = null;
  }
  // 使用 GeoJsonLayer 加载查询结果
  geoJsonLayer = new mars3d.layer.GeoJsonLayer({
    data: simplifiedGeojson,
    symbol: {
      type: "polygonP",
      styleOptions: {
        fill: true,
        color: "#37e8f1",
        opacity: 0.45,
        outline: true,
        label: {
          // 面中心点，显示文字的配置
          text: "{town}", // 对应的属性名称
          opacity: 1,
          font_size: 20,
          color: "#fcc31f",

          font_family: "楷体",
          outline: true,
          outlineColor: "#000000",
          outlineWidth: 3,

          background: false,
          backgroundColor: "#000000",
          backgroundOpacity: 0.1,

          font_weight: "normal",
          font_style: "normal",

          scaleByDistance: true,
          scaleByDistance_far: 20000000,
          scaleByDistance_farValue: 0.1,
          scaleByDistance_near: 1000,
          scaleByDistance_nearValue: 1,

          distanceDisplayCondition: false,
          distanceDisplayCondition_far: 10000,
          distanceDisplayCondition_near: 0,
          visibleDepth: false,
        },
        outlineStyle: {
          color: "#6bcfd4",
          width: 2,
          opacity: 1,
        },
        highlight: {
          opacity: 0.3,
          outlineStyle: {
            color: "#37e8f1",
            width: 7,
            opacity: 1.0,
            addHeight: 100,
          },
        },
      },
    },
    flyTo: true,
  });

  // 测试修改className
  geoJsonLayer.on(mars3d.EventType.click, function (event) {
    const graphic = event.graphic; // 被点击的图形
    const attr = graphic.attr; // 获取属性字段信息
    console.log("点击了要素：", attr);

    eventTarget.fire("getInfo", attr);

    parentCode = attr.village || attr.town_code;
    const point = mars3d.LngLatPoint.fromCartesian(graphic.center);
    parentHeightCenter = point;

    clickDataArr.push({
      parentCode,
      parentHeightCenter,
    });
    infoArr.push({
      info: attr,
    });

    // console.log("clickDataArr------", clickDataArr);
    // console.log("infoArr------", infoArr);

    clickSetView(map, event);
    if (fun) {
      fun(attr);
    }
  });
  map.addLayer(geoJsonLayer);
  return geoJsonLayer;
}

let viewHeightLevel = 0;
const viewHeightArr = [98900, 60750, 23395, 4328];
let parentCode: string = "";
let parentHeightCenter: any;

const clickDataArr: any[] = [];
let infoArr: any[] = [];

function createGeoJsonLayer(data: any) {
  if (geoJsonLayer) {
    thisMap.removeLayer(geoJsonLayer, false); // true 表示彻底销毁
    geoJsonLayer = null;
  }
  geoJsonLayer = new mars3d.layer.GeoJsonLayer({
    data,
    symbol: {
      type: "polygonP",
      styleOptions: {
        fill: true,
        color: "#156b82",
        opacity: 0.1,
        outline: true,
        label: {
          // 面中心点，显示文字的配置
          text: "{town}", // 对应的属性名称
          opacity: 1,
          font_size: 20,
          color: "#fcc31f",

          font_family: "楷体",
          outline: true,
          outlineColor: "#000000",
          outlineWidth: 3,

          background: false,
          backgroundColor: "#000000",
          backgroundOpacity: 0.1,

          font_weight: "normal",
          font_style: "normal",

          scaleByDistance: true,
          scaleByDistance_far: 20000000,
          scaleByDistance_farValue: 0.1,
          scaleByDistance_near: 1000,
          scaleByDistance_nearValue: 1,

          distanceDisplayCondition: false,
          distanceDisplayCondition_far: 10000,
          distanceDisplayCondition_near: 0,
          visibleDepth: false,
        },
        highlight: {
          outlineStyle: {
            color: "#37e9f2",
            width: 7,
            opacity: 1.0,
            addHeight: 100,
          },
        },
      },
      callback: function (attr: any) {
        if (viewHeightLevel === 1) {
          return {
            opacity: 0.8,
            highlight: {
              outlineStyle: {
                color: "#37e9f2",
                width: 7,
                opacity: 1.0,
                addHeight: 100,
              },
            },
            label: {
              text: "{town}",
            },
            outlineStyle: {
              color: "#34d4f0",
              width: 3,
              opacity: 1,
            },
          };
        } else if (
          viewHeightLevel === 2 &&
          attr.town_code === parentCode
        ) {
          return {
            opacity: 0.8,
            highlight: {
              outlineStyle: {
                color: "#37e9f2",
                width: 7,
                opacity: 1.0,
                addHeight: 100,
              },
            },
            label: {
              text: "{village}",
            },
            outlineStyle: {
              color: "#34d4f0",
              width: 3,
              opacity: 1,
            },
          };
        } else if (viewHeightLevel === 3 && attr.village === parentCode) {
          return {
            opacity: 0.25,
            color: "#f74622",
            label: {
              text: "{name}",
            },
            outlineStyle: {
              color: "#f74622",
              width: 3,
              opacity: 1,
            },
          };
        } else {
          return {
            opacity: 0,
            outline: false,
            label: {
              show: false,
            },
          };
        }
      },
    },
  });

  geoJsonLayer.on(mars3d.EventType.click, function (event) {
    const graphic = event.graphic; // 被点击的图形
    const attr = graphic.attr; // 获取属性字段信息

    eventTarget.fire("getInfo", attr);
    parentCode = attr.village || attr.town_code;
    const point = mars3d.LngLatPoint.fromCartesian(graphic.center);
    parentHeightCenter = point;

    if (clickDataArr.length < 2) {
      clickDataArr.push({
        parentCode,
        parentHeightCenter,
      });

      infoArr.push({
        info: attr,
      });
    }

    // console.log("clickDataArr------", clickDataArr);
    // console.log("infoArr------", infoArr);

    clickSetView(thisMap, event);
  });
  thisMap.addLayer(geoJsonLayer);
  return geoJsonLayer;
}

function clickSetView(map: mars3d.Map, event: any) {
  const attr = event.graphic.attr;
  if (attr.gridlevel) {
    //点击的是 网格（四级）
  } else if (attr.village) {
    //点击的是 社区，村（三级）,进入四级
    viewHeightLevel = 3;
  } else if (attr.town) {
    //点击的是  镇，街道（二级）,进入三级
    viewHeightLevel = 2;
  } else {
    //点击的是  市（一级）,进入二级
    viewHeightLevel = 1;
  }

  const data = dataArr[viewHeightLevel];

  // console.log("clickSetView----viewHeightLevel", viewHeightLevel)
  geoJsonLayer = createGeoJsonLayer(data);

  map.setCameraView({
    lng: parentHeightCenter && parentHeightCenter.lng,
    lat: parentHeightCenter && parentHeightCenter.lat,
    alt: viewHeightArr[viewHeightLevel],
  });
}

export function back() {
  if (viewHeightLevel <= 1) {
    return;
  }
  const data = dataArr[--viewHeightLevel];

  // console.log("back----viewHeightLevel", viewHeightLevel);

  clickDataArr.pop();
  const index = viewHeightLevel - 1;
  const infoData = infoArr[index];
  if (infoArr.length > 1) {
    infoArr.pop();
  }
  eventTarget.fire("getInfo", infoData);

  const parentData = clickDataArr[clickDataArr.length - 1];
  if (parentData && parentData.parentCode) {
    parentCode = parentData.parentCode;
    parentHeightCenter = parentData.parentHeightCenter;
  }

  // 使用 GeoJsonLayer 加载查询结果
  geoJsonLayer = createGeoJsonLayer(data);
  if (viewHeightLevel === 0 || viewHeightLevel === 1) {
    thisMap.setCameraView({
      lng: 103.412165,
      lat: 30.386399,
      alt: 98900,
    });
  } else if (viewHeightLevel === 2) {
    thisMap.setCameraView({
      lng: parentHeightCenter.lng,
      lat: parentHeightCenter.lat,
      alt: 23395,
    });
  }
}

// 添加点位
export function addDemoGraphic(
  graphicLayer: any,
  point: any,
  fun?: (pointData: any) => void
) {
  const graphic = new mars3d.graphic.BillboardEntity({
    position: point.position,
    style: {
      image: "/img/flag.png",
    },
    attr: point.title,
  });

  graphic.bindPopup(
    function (event) {
      console.log(event);
      return `<div class="qiong-lai-title">${event.graphic.attr}</div>
                        <div class="qiong-lai-body">
                          <div class="qiong-lai-content"></div>
                        </div>
      `;
    },
    {
      template: `<div class="qiong-lai-popup">
                        {content}
                        <span class="closeButton" >×</span>
                </div>`,
    }
  );

  // 测试修改className
  graphic.on("click", function (e) {
    console.log("单击回调");
    if (fun) {
      // fun(point);
    }
  });
  graphicLayer.addGraphic(graphic);
  return graphic;
}

// 通过id删除
export function removeDemoGraphicById(graphicLayer: any, id: number) {
  const graphic = graphicLayer.getGraphics().find((g: any) => g.id === id);
  if (graphic) {
    graphicLayer.removeGraphic(graphic);
  } else {
    console.warn(`未找到 id 为 ${id} 的图形`);
  }
}
